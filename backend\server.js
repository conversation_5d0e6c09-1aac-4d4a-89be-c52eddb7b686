const express = require('express');
const bodyParser = require('body-parser');
const WebSocket = require('ws');
require('dotenv').config();

const app = express();
app.use(bodyParser.json());

let alertClients = [];

// WebSocket Server
const wss = new WebSocket.Server({ port: 8080 });
wss.on('connection', (ws) => {
  alertClients.push(ws);
  ws.on('close', () => {
    alertClients = alertClients.filter(client => client !== ws);
  });
});

// Twitch Webhook Receiver
app.post('/twitch/webhook', (req, res) => {
  const event = req.body.event;
  if (event && event.user_name) {
    const alertData = {
      type: req.body.subscription.type,
      user: event.user_name
    };
    alertClients.forEach(client => client.send(JSON.stringify(alertData)));
    res.sendStatus(200);
  } else {
    res.sendStatus(204);
  }
});

// Webhook Subscription Verification (GET request)
app.get('/twitch/webhook', (req, res) => {
  const challenge = req.query['hub.challenge'];
  res.send(challenge);
});

// Start Server
app.listen(3000, () => {
  console.log('Twitch Webhook server is running on http://localhost:3000');
});
