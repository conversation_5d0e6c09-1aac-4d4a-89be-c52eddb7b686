function triggerAlert(type, user) {
  const box = document.getElementById('alert-box');
  const title = document.getElementById('alert-title');
  const msg = document.getElementById('alert-msg');

  if (type === 'follow') {
    title.textContent = 'New Follower!';
    msg.textContent = `Thanks for the follow, @${user}!`;
  } else if (type === 'sub') {
    title.textContent = 'New Subscriber!';
    msg.textContent = `Much love to @${user} for subscribing!`;
  }

  box.classList.remove('hidden');

  // Hide after 5 seconds
  setTimeout(() => {
    box.classList.add('hidden');
  }, 5000);
}

const socket = new WebSocket("ws://localhost:8080");

socket.onmessage = (event) => {
  const { type, user } = JSON.parse(event.data);
  triggerAlert(type, user);
};
